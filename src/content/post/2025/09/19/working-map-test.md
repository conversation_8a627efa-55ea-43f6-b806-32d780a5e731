---
title: "Working Google Map Test"
description: "Test with a known working Google Maps URL"
publishDate: "2025-09-19"
tags: ["test", "googlemap"]
draft: false
---

# Working Google Map Test

First, let's test with a simple address:

::googlemap{address="Paris"}

## Manual Test

Here's a manual iframe to verify the URL format works:

<iframe src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d459.2700420313287!2d-113.19060778257229!3d36.42523052428919!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e1!3m2!1szh-CN!2sus!4v1757949781743!5m2!1szh-CN!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>

## Plugin Test

Now let's test our plugin:

::googlemap{address="New York"}

Check the browser console for any error messages.
