---
title: "Google Map Plugin Test"
description: "Testing the new Google Map plugin for embedding maps in markdown"
publishDate: "2025-09-19"
tags: ["test", "plugin", "googlemap"]
draft: false
---

# Google Map Plugin Test

This post is used to test the new Google Map plugin that allows embedding Google Maps directly in markdown using a simple directive syntax.

## Basic Usage

You can embed a Google Map by using the `::googlemap` directive with an address:

::googlemap{address="New York, NY, USA"}

## Different Locations

Here are some examples with different locations:

### Famous Landmarks

::googlemap{address="Eiffel Tower, Paris, France"}

### Specific Addresses

::googlemap{address="1600 Amphitheatre Parkway, Mountain View, CA"}

### Using location attribute (alternative syntax)

::googlemap{location="Tokyo, Japan"}

## Features

The Google Map plugin includes:

- **Non-blocking loading**: Maps load asynchronously and won't block page rendering
- **Error handling**: If a map fails to load, it gracefully hides without taking up space
- **Responsive design**: Maps adapt to different screen sizes
- **Loading indicators**: Shows a spinner while the map is loading
- **Accessibility**: Proper focus management and keyboard navigation

## Technical Details

The plugin uses:
- Google Maps Embed API (generic version that doesn't require API key)
- Lazy loading for better performance
- CSS animations for smooth transitions
- Responsive iframe sizing

## Test Cases

### Valid Address
::googlemap{address="Grand Canyon National Park, Arizona, USA"}

### Another Valid Location
::googlemap{address="Sydney Opera House, Sydney, Australia"}

---

This test file demonstrates various use cases of the Google Map plugin. The maps should load without blocking the page and gracefully handle any loading errors.
