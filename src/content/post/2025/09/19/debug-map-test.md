---
title: "Debug Google Map Test"
description: "Debug version to understand what's happening"
publishDate: "2025-09-19"
tags: ["test", "googlemap", "debug"]
draft: false
---

# Debug Google Map Test

This page will help us debug the Google Map plugin.

## Test 1: Simple Address

::googlemap{address="Paris"}

## Test 2: Another Address

::googlemap{address="London"}

## Instructions

1. Open browser developer tools (F12)
2. Go to the Console tab
3. Look for messages starting with `[GOOGLE-MAP]`
4. Check the Network tab to see if the iframe requests are being made
5. Look for any error messages

## Expected Behavior

- You should see loading spinners initially
- After a few seconds, maps should appear OR error messages should show in console
- If maps fail to load, the entire map containers should disappear (not take up space)

## Troubleshooting

If you see "Failed to load map" for all maps:
- Check if Google Maps is accessible in your region
- Check browser console for specific error messages
- Try opening the iframe URL directly in a new tab
- The URL format being used is: `https://www.google.com/maps/embed?pb=...&q=ADDRESS`

## Manual Test URL

Try this URL directly in your browser:
```
https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3048.398!2d0!3d0!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM!5e0!3m2!1sen!2sus!4v1234567890!5m2!1sen!2sus&q=Paris
```
