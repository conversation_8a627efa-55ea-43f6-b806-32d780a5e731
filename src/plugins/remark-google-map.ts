import type { Root } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";
import { h, isNodeDirective } from "../utils/remark";

const DIRECTIVE_NAME = "googlemap";

export const remarkGoogleMap: Plugin<[], Root> = () => (tree) => {
	visit(tree, (node, index, parent) => {
		if (!parent || index === undefined || !isNodeDirective(node)) return;

		// We only want a leaf directive named DIRECTIVE_NAME
		if (node.type !== "leafDirective" || node.name !== DIRECTIVE_NAME) return;

		let address = node.attributes?.address ?? node.attributes?.location ?? null;
		let lat = node.attributes?.lat ?? node.attributes?.latitude ?? null;
		let lng = node.attributes?.lng ?? node.attributes?.longitude ?? null;
		let title = node.attributes?.title ?? null;

		// Must have either address or coordinates
		if (!address && (!lat || !lng)) return;

		// Clean up the inputs
		if (address) address = address.trim();
		if (lat) lat = lat.trim();
		if (lng) lng = lng.trim();
		if (title) title = title.trim();

		// Generate display text and map query
		let displayText = title || address || `${lat}, ${lng}`;
		let mapQuery = address || `${lat},${lng}`;
		
		const SimpleUUID = `GM-${crypto.randomUUID()}`;

		// Create a script that will handle the iframe loading
		const script = h("script", {}, [
			{
				type: "text",
				value: `
				(function() {
					const container = document.getElementById('${SimpleUUID}');
					const iframe = container.querySelector('.gm-iframe');
					const loadingEl = container.querySelector('.gm-loading');
					const errorEl = container.querySelector('.gm-error');
					const mapQuery = '${mapQuery.replace(/'/g, "\\'")}';
					const displayText = '${displayText.replace(/'/g, "\\'")}';

					console.log('[GOOGLE-MAP] Initializing map for: ' + displayText + ' (query: ' + mapQuery + ')');

					// Set up iframe load handlers
					iframe.onload = function() {
						// Check if the iframe actually loaded content
						try {
							// If we can access the iframe content, it loaded successfully
							console.log('[GOOGLE-MAP] Map loaded successfully for: ' + displayText);
							container.classList.remove('gm-loading');
							loadingEl.style.display = 'none';
						} catch (e) {
							// If we can't access content, it might be blocked
							console.warn('[GOOGLE-MAP] Map may be blocked for: ' + address, e);
						}
					};

					iframe.onerror = function(e) {
						console.warn('[GOOGLE-MAP] Error loading map for: ' + address, e);
						container.classList.remove('gm-loading');
						container.classList.add('gm-error');
						loadingEl.style.display = 'none';
						errorEl.style.display = 'block';
						iframe.style.display = 'none';
					};

					// Set the iframe src to start loading
					// Use the most reliable Google Maps embed URL format
					var mapUrl = 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3048.398!2d0!3d0!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM!5e0!3m2!1sen!2sus!4v1234567890!5m2!1sen!2sus&q=' + encodeURIComponent(address);
					console.log('[GOOGLE-MAP] Loading URL: ' + mapUrl);
					iframe.src = mapUrl;

					// Fallback timeout - if iframe doesn't load within 10 seconds, show error
					setTimeout(function() {
						if (container.classList.contains('gm-loading')) {
							console.warn('[GOOGLE-MAP] Timeout loading map for: ' + address);
							container.classList.remove('gm-loading');
							container.classList.add('gm-error');
							loadingEl.style.display = 'none';
							errorEl.style.display = 'block';
							iframe.style.display = 'none';
						}
					}, 10000);
				})();
			`,
			},
		]);

		// Create the loading indicator
		const loadingIndicator = h("div", { class: "gm-loading" }, [
			h("div", { class: "gm-loading-spinner" }),
			h("div", { class: "gm-loading-text" }, [
				{ type: "text", value: "Loading map..." },
			]),
		]);

		// Create the error message
		const errorMessage = h("div", { class: "gm-error", style: "display: none;" }, [
			h("div", { class: "gm-error-icon" }),
			h("div", { class: "gm-error-text" }, [
				{ type: "text", value: "Failed to load map" },
			]),
		]);

		// Create the iframe (initially without src to prevent blocking)
		const mapIframe = h("iframe", {
			class: "gm-iframe",
			width: "600",
			height: "450",
			style: "border:0;",
			allowfullscreen: "true",
			loading: "lazy",
			referrerpolicy: "no-referrer-when-downgrade",
			title: `Map of ${address}`
		});

		// Create the address display
		const addressDisplay = h("div", { class: "gm-address" }, [
			h("span", { class: "gm-address-icon" }),
			h("span", { class: "gm-address-text" }, [
				{ type: "text", value: address },
			]),
		]);

		parent.children.splice(
			index,
			1,
			h("div", { id: SimpleUUID, class: "google-map gm-loading" }, [
				addressDisplay,
				loadingIndicator,
				errorMessage,
				mapIframe,
				script,
			]),
		);
	});
};
