import type { Root } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";
import { h, isNodeDirective } from "../utils/remark";

const DIRECTIVE_NAME = "googlemap";

export const remarkGoogleMap: Plugin<[], Root> = () => (tree) => {
	visit(tree, (node, index, parent) => {
		if (!parent || index === undefined || !isNodeDirective(node)) return;

		// We only want a leaf directive named DIRECTIVE_NAME
		if (node.type !== "leafDirective" || node.name !== DIRECTIVE_NAME) return;

		let address = node.attributes?.address ?? node.attributes?.location ?? null;
		if (!address) return; // Leave the directive as-is if no address is provided

		// Clean up the address
		address = address.trim();
		
		const SimpleUUID = `GM-${crypto.randomUUID()}`;

		// Create a script that will handle the iframe loading
		const script = h("script", {}, [
			{
				type: "text",
				value: `
				(function() {
					const container = document.getElementById('${SimpleUUID}');
					const iframe = container.querySelector('.gm-iframe');
					const loadingEl = container.querySelector('.gm-loading');
					const errorEl = container.querySelector('.gm-error');
					
					// Set up iframe load handlers
					iframe.onload = function() {
						container.classList.remove('gm-loading');
						loadingEl.style.display = 'none';
					};
					
					iframe.onerror = function() {
						container.classList.remove('gm-loading');
						container.classList.add('gm-error');
						loadingEl.style.display = 'none';
						errorEl.style.display = 'block';
						iframe.style.display = 'none';
					};
					
					// Set the iframe src to start loading
					iframe.src = 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3048.398!2d0!3d0!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zM!5e0!3m2!1sen!2s!4v1234567890!5m2!1sen!2s&q=${encodeURIComponent(address)}';
					
					// Fallback timeout - if iframe doesn't load within 10 seconds, show error
					setTimeout(function() {
						if (container.classList.contains('gm-loading')) {
							container.classList.remove('gm-loading');
							container.classList.add('gm-error');
							loadingEl.style.display = 'none';
							errorEl.style.display = 'block';
							iframe.style.display = 'none';
						}
					}, 10000);
				})();
			`,
			},
		]);

		// Create the loading indicator
		const loadingIndicator = h("div", { class: "gm-loading" }, [
			h("div", { class: "gm-loading-spinner" }),
			h("div", { class: "gm-loading-text" }, [
				{ type: "text", value: "Loading map..." },
			]),
		]);

		// Create the error message
		const errorMessage = h("div", { class: "gm-error", style: "display: none;" }, [
			h("div", { class: "gm-error-icon" }),
			h("div", { class: "gm-error-text" }, [
				{ type: "text", value: "Failed to load map" },
			]),
		]);

		// Create the iframe (initially without src to prevent blocking)
		const mapIframe = h("iframe", {
			class: "gm-iframe",
			width: "600",
			height: "450",
			style: "border:0;",
			allowfullscreen: "",
			loading: "lazy",
			referrerpolicy: "no-referrer-when-downgrade"
		});

		// Create the address display
		const addressDisplay = h("div", { class: "gm-address" }, [
			h("span", { class: "gm-address-icon" }),
			h("span", { class: "gm-address-text" }, [
				{ type: "text", value: address },
			]),
		]);

		parent.children.splice(
			index,
			1,
			h("div", { id: SimpleUUID, class: "google-map gm-loading" }, [
				addressDisplay,
				loadingIndicator,
				errorMessage,
				mapIframe,
				script,
			]),
		);
	});
};
