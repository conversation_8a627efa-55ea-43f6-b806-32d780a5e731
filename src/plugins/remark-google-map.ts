import type { Root } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";
import { h, isNodeDirective } from "../utils/remark";

const DIRECTIVE_NAME = "googlemap";

export const remarkGoogleMap: Plugin<[], Root> = () => (tree) => {
	visit(tree, (node, index, parent) => {
		if (!parent || index === undefined || !isNodeDirective(node)) return;

		// We only want a leaf directive named DIRECTIVE_NAME
		if (node.type !== "leafDirective" || node.name !== DIRECTIVE_NAME) return;

		let address = node.attributes?.address ?? node.attributes?.location ?? null;
		if (!address) return; // Leave the directive as-is if no address is provided

		// Clean up the address
		address = address.trim();
		
		const SimpleUUID = `GM-${crypto.randomUUID()}`;

		// Create a script that will handle the iframe loading
		const script = h("script", {}, [
			{
				type: "text",
				value: `
				(function() {
					const container = document.getElementById('${SimpleUUID}');
					const iframe = container.querySelector('.gm-iframe');
					const loadingEl = container.querySelector('.gm-loading');
					const errorEl = container.querySelector('.gm-error');
					
					// Set up iframe load handlers
					iframe.onload = function() {
						console.log('[GOOGLE-MAP] Map loaded successfully for: ' + '${address}');
						container.classList.remove('gm-loading');
						loadingEl.style.display = 'none';
					};

					iframe.onerror = function(e) {
						console.warn('[GOOGLE-MAP] Error loading map for: ' + '${address}', e);
						container.classList.remove('gm-loading');
						container.classList.add('gm-error');
						loadingEl.style.display = 'none';
						errorEl.style.display = 'block';
						iframe.style.display = 'none';
					};
					
					// Set the iframe src to start loading
					// Use Google Maps search URL - this is the most reliable method
					iframe.src = 'https://maps.google.com/maps?q=' + encodeURIComponent(address) + '&output=embed';
					
					// Fallback timeout - if iframe doesn't load within 10 seconds, show error
					setTimeout(function() {
						if (container.classList.contains('gm-loading')) {
							console.warn('[GOOGLE-MAP] Timeout loading map for: ' + '${address}');
							container.classList.remove('gm-loading');
							container.classList.add('gm-error');
							loadingEl.style.display = 'none';
							errorEl.style.display = 'block';
							iframe.style.display = 'none';
						}
					}, 10000);
				})();
			`,
			},
		]);

		// Create the loading indicator
		const loadingIndicator = h("div", { class: "gm-loading" }, [
			h("div", { class: "gm-loading-spinner" }),
			h("div", { class: "gm-loading-text" }, [
				{ type: "text", value: "Loading map..." },
			]),
		]);

		// Create the error message
		const errorMessage = h("div", { class: "gm-error", style: "display: none;" }, [
			h("div", { class: "gm-error-icon" }),
			h("div", { class: "gm-error-text" }, [
				{ type: "text", value: "Failed to load map" },
			]),
		]);

		// Create the iframe (initially without src to prevent blocking)
		const mapIframe = h("iframe", {
			class: "gm-iframe",
			width: "600",
			height: "450",
			style: "border:0;",
			allowfullscreen: "true",
			loading: "lazy",
			referrerpolicy: "no-referrer-when-downgrade",
			title: `Map of ${address}`
		});

		// Create the address display
		const addressDisplay = h("div", { class: "gm-address" }, [
			h("span", { class: "gm-address-icon" }),
			h("span", { class: "gm-address-text" }, [
				{ type: "text", value: address },
			]),
		]);

		parent.children.splice(
			index,
			1,
			h("div", { id: SimpleUUID, class: "google-map gm-loading" }, [
				addressDisplay,
				loadingIndicator,
				errorMessage,
				mapIframe,
				script,
			]),
		);
	});
};
