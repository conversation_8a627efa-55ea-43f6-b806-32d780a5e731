.google-map {
	@apply bg-global-text/5 rounded-md px-4 py-3 mb-4;
	max-width: 100%;
	overflow: hidden;

	.gm-address {
		@apply flex items-center gap-2 text-base mb-3;
		
		.gm-address-icon {
			@apply bg-global-text h-5 w-5 flex-none;
			mask: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' height='20' viewBox='0 0 24 24' width='20'><path d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/></svg>") center center/16px auto no-repeat;
		}
		
		.gm-address-text {
			@apply text-global-text font-medium;
		}
	}

	.gm-iframe {
		@apply w-full rounded-md;
		max-width: 100%;
		height: 450px;
		
		@media (max-width: 640px) {
			height: 300px;
		}
	}

	.gm-loading {
		@apply flex flex-col items-center justify-center py-8 text-center;
		min-height: 200px;
		
		.gm-loading-spinner {
			@apply w-8 h-8 border-4 border-global-text/20 border-t-accent rounded-full animate-spin mb-3;
		}
		
		.gm-loading-text {
			@apply text-global-text/70 text-sm;
		}
	}

	.gm-error {
		@apply flex flex-col items-center justify-center py-8 text-center;
		min-height: 200px;
		
		.gm-error-icon {
			@apply bg-red-500 w-8 h-8 mb-3 rounded-full;
			mask: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' height='24' viewBox='0 0 24 24' width='24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/></svg>") center center/20px auto no-repeat;
		}
		
		.gm-error-text {
			@apply text-red-500 text-sm;
		}
	}

	/* Loading state styles */
	&.gm-loading {
		.gm-iframe {
			@apply hidden;
		}
	}

	/* Error state styles */
	&.gm-error {
		.gm-loading {
			@apply hidden;
		}
		
		.gm-iframe {
			@apply hidden;
		}
	}

	/* Responsive design */
	@media (max-width: 768px) {
		@apply px-3 py-2;
		
		.gm-address {
			@apply text-sm mb-2;
		}
	}

	/* Dark mode adjustments */
	[data-theme="dark"] & {
		.gm-loading-spinner {
			@apply border-global-text/30 border-t-accent;
		}
	}

	/* Accessibility improvements */
	.gm-iframe:focus {
		@apply outline-2 outline-accent outline-offset-2;
	}

	/* Animation for smooth transitions */
	.gm-iframe,
	.gm-loading,
	.gm-error {
		transition: opacity 0.3s ease-in-out;
	}

	/* Ensure the map doesn't break layout */
	.gm-iframe {
		display: block;
		border: none;
		background: var(--color-global-bg);
	}
}
